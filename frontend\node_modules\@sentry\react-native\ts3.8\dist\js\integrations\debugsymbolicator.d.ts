import type { EventProcessor, Hub, Integration } from '@sentry/types';
/**
 * React Native Error
 */
export type ReactNativeError = Error & {
    framesToPop?: number;
    jsEngine?: string;
    preventSymbolication?: boolean;
    componentStack?: string;
};
/** Tries to symbolicate the JS stack trace on the device. */
export declare class DebugSymbolicator implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    /**
     * @inheritDoc
     */
    name: string;
    /**
     * @inheritDoc
     */
    setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void;
    /**
     * Symbolicates the stack on the device talking to local dev server.
     * Mutates the passed event.
     */
    private _symbolicate;
    /**
     * Converts ReactNativeFrames to frames in the Sentry format
     * @param frames ReactNativeFrame[]
     */
    private _convertReactNativeFramesToSentryFrames;
    /**
     * Replaces the frames in the exception of a error.
     * @param event Event
     * @param frames StackFrame[]
     */
    private _replaceExceptionFramesInEvent;
    /**
     * Replaces the frames in the thread of a message.
     * @param event Event
     * @param frames StackFrame[]
     */
    private _replaceThreadFramesInEvent;
    /**
     * This tries to add source context for in_app Frames
     *
     * @param frame StackFrame
     * @param getDevServer function from RN to get DevServer URL
     */
    private _addSourceContext;
    /**
     * Get source context for segment
     */
    private _fetchSourceContext;
    /**
     * Loads and calls RN Core Devtools parseErrorStack function.
     */
    private _parseErrorStack;
    /**
     * Loads and calls RN Core Devtools symbolicateStackTrace function.
     */
    private _symbolicateStackTrace;
    /**
     * Loads and returns the RN DevServer URL.
     */
    private _getDevServer;
}
//# sourceMappingURL=debugsymbolicator.d.ts.map
