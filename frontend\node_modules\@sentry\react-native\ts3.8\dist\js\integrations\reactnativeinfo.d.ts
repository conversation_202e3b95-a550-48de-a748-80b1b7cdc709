import type { Context, EventProcessor, Integration } from '@sentry/types';
export interface ReactNativeContext extends Context {
    js_engine?: string;
    turbo_module: boolean;
    fabric: boolean;
    expo: boolean;
    hermes_version?: string;
    react_native_version?: string;
    component_stack?: string;
    hermes_debug_info?: boolean;
    expo_go_version?: string;
    expo_sdk_version?: string;
}
/** Loads React Native context at runtime */
export declare class ReactNativeInfo implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    /**
     * @inheritDoc
     */
    name: string;
    /**
     * @inheritDoc
     */
    setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void): void;
}
//# sourceMappingURL=reactnativeinfo.d.ts.map
