import type { EventHint, EventProcessor, Integration } from '@sentry/types';
/** Adds screenshots to error events */
export declare class Screenshot implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    /**
     * @inheritDoc
     */
    name: string;
    /**
     * If enabled attaches a screenshot to the event hint.
     *
     * @deprecated Screenshots are now added in global event processor.
     */
    static attachScreenshotToEventHint(hint: EventHint, { attachScreenshot }: {
        attachScreenshot?: boolean;
    }): PromiseLike<EventHint>;
    /**
     * @inheritDoc
     */
    setupOnce(addGlobalEventProcessor: (e: EventProcessor) => void): void;
}
//# sourceMappingURL=screenshot.d.ts.map
