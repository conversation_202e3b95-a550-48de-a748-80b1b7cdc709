/**
 * Loading Hook for ExoBank Frontend
 * 
 * This hook provides a convenient way to access and manage
 * loading states throughout the application.
 */

import { useState, useEffect, useCallback } from 'react';
import loadingService, { LoadingState } from '../services/loadingService';

interface UseLoadingOptions {
  initialMessage?: string;
  showProgress?: boolean;
}

interface UseLoadingResult {
  isLoading: boolean;
  progress?: number;
  message?: string;
  startLoading: (message?: string) => void;
  stopLoading: () => void;
  updateProgress: (progress: number, message?: string) => void;
  withLoading: <T>(asyncFn: () => Promise<T>, message?: string) => Promise<T>;
  withProgress: <T>(
    asyncFn: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
    message?: string
  ) => Promise<T>;
}

/**
 * Hook for managing loading states
 * @param loadingKey Unique identifier for this loading operation
 * @param options Configuration options
 * @returns Loading state and control functions
 */
export function useLoading(
  loadingKey: string,
  options: UseLoadingOptions = {}
): UseLoadingResult {
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    progress: options.showProgress ? 0 : undefined,
    message: options.initialMessage,
  });

  useEffect(() => {
    // Get initial state
    const initialState = loadingService.getLoadingState(loadingKey);
    setState(initialState);

    // Subscribe to loading state changes
    const unsubscribe = loadingService.addListener(loadingKey, (newState) => {
      setState(newState);
    });

    return () => {
      unsubscribe();
    };
  }, [loadingKey]);

  const startLoading = useCallback((message?: string) => {
    loadingService.startLoading(loadingKey, {
      message: message || options.initialMessage,
      showProgress: options.showProgress,
    });
  }, [loadingKey, options.initialMessage, options.showProgress]);

  const stopLoading = useCallback(() => {
    loadingService.stopLoading(loadingKey);
  }, [loadingKey]);

  const updateProgress = useCallback((progress: number, message?: string) => {
    loadingService.updateProgress(loadingKey, progress, message);
  }, [loadingKey]);

  const withLoading = useCallback(async <T>(asyncFn: () => Promise<T>, message?: string): Promise<T> => {
    return loadingService.withLoading(loadingKey, asyncFn, {
      message: message || options.initialMessage,
      showProgress: options.showProgress,
    });
  }, [loadingKey, options.initialMessage, options.showProgress]);

  const withProgress = useCallback(async <T>(
    asyncFn: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
    message?: string
  ): Promise<T> => {
    return loadingService.withProgress(loadingKey, asyncFn, {
      message: message || options.initialMessage,
    });
  }, [loadingKey, options.initialMessage]);

  return {
    isLoading: state.isLoading,
    progress: state.progress,
    message: state.message,
    startLoading,
    stopLoading,
    updateProgress,
    withLoading,
    withProgress,
  };
}

export default useLoading;

// API-specific loading hook interface
interface UseApiLoadingOptions {
  operation?: string;
  message?: string;
}

interface UseApiLoadingResult {
  executeWithLoading: <T>(
    operationKey: string,
    asyncFn: () => Promise<T>,
    options?: UseApiLoadingOptions
  ) => Promise<T>;
  isOperationLoading: (operationKey?: string) => boolean;
  getActiveOperations: () => string[];
}

/**
 * Hook for managing API-specific loading states
 * This hook provides a simplified interface for API operations
 * @returns API loading state management functions
 */
export function useApiLoading(): UseApiLoadingResult {
  const executeWithLoading = useCallback(async <T>(
    operationKey: string,
    asyncFn: () => Promise<T>,
    options: UseApiLoadingOptions = {}
  ): Promise<T> => {
    return loadingService.withLoading(operationKey, asyncFn, {
      operation: options.operation || operationKey,
      message: options.message || `Loading ${operationKey}...`,
    });
  }, []);

  const isOperationLoading = useCallback((operationKey?: string): boolean => {
    if (operationKey) {
      const state = loadingService.getLoadingState(operationKey);
      return state.isLoading;
    }
    return loadingService.isAnyLoading();
  }, []);

  const getActiveOperations = useCallback((): string[] => {
    return loadingService.getActiveOperations();
  }, []);

  return {
    executeWithLoading,
    isOperationLoading,
    getActiveOperations,
  };
}