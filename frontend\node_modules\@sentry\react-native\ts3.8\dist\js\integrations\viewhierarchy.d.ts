import type { EventProcessor, Integration } from '@sentry/types';
/** Adds ViewHierarchy to error events */
export declare class ViewHierarchy implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    private static _fileName;
    private static _contentType;
    private static _attachmentType;
    /**
     * @inheritDoc
     */
    name: string;
    /**
     * @inheritDoc
     */
    setupOnce(addGlobalEventProcessor: (e: EventProcessor) => void): void;
}
//# sourceMappingURL=viewhierarchy.d.ts.map
