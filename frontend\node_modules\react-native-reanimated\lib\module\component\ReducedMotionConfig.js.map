{"version": 3, "names": ["useEffect", "ReduceMotion", "logger", "isReducedMotionEnabledInSystem", "ReducedMotionManager", "ReducedMotionConfig", "mode", "__DEV__", "warn", "wasEnabled", "jsValue", "System", "setEnabled", "Always", "Never"], "sourceRoot": "../../../src", "sources": ["component/ReducedMotionConfig.tsx"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,OAAO;AAEjC,SAASC,YAAY,QAAQ,mBAAgB;AAC7C,SAASC,MAAM,QAAQ,oBAAW;AAClC,SACEC,8BAA8B,EAC9BC,oBAAoB,QACf,qBAAkB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAAC;EAAEC;AAA6B,CAAC,EAAE;EACpEN,SAAS,CAAC,MAAM;IACd,IAAI,CAACO,OAAO,EAAE;MACZ;IACF;IACAL,MAAM,CAACM,IAAI,CAAC,oDAAoDF,IAAI,IAAI,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EAENN,SAAS,CAAC,MAAM;IACd,MAAMS,UAAU,GAAGL,oBAAoB,CAACM,OAAO;IAC/C,QAAQJ,IAAI;MACV,KAAKL,YAAY,CAACU,MAAM;QACtBP,oBAAoB,CAACQ,UAAU,CAACT,8BAA8B,CAAC,CAAC,CAAC;QACjE;MACF,KAAKF,YAAY,CAACY,MAAM;QACtBT,oBAAoB,CAACQ,UAAU,CAAC,IAAI,CAAC;QACrC;MACF,KAAKX,YAAY,CAACa,KAAK;QACrBV,oBAAoB,CAACQ,UAAU,CAAC,KAAK,CAAC;QACtC;IACJ;IACA,OAAO,MAAM;MACXR,oBAAoB,CAACQ,UAAU,CAACH,UAAU,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,CAACH,IAAI,CAAC,CAAC;EAEV,OAAO,IAAI;AACb", "ignoreList": []}