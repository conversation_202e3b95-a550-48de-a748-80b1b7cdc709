{"version": 3, "names": ["SLOPE_FACTOR", "VELOCITY_EPS", "rigidDecay", "animation", "now", "config", "lastTimestamp", "startTimestamp", "initialVelocity", "current", "velocity", "deltaTime", "Math", "min", "v", "exp", "deceleration", "velocityFactor", "clamp", "abs"], "sourceRoot": "../../../../src", "sources": ["animation/decay/rigidDecay.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,YAAY,EAAEC,YAAY,QAAQ,YAAS;AAEpD,OAAO,SAASC,UAAUA,CACxBC,SAA8B,EAC9BC,GAAW,EACXC,MAA0B,EACjB;EACT,SAAS;;EACT,MAAM;IAAEC,aAAa;IAAEC,cAAc;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAS,CAAC,GACzEP,SAAS;EAEX,MAAMQ,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACT,GAAG,GAAGE,aAAa,EAAE,EAAE,CAAC;EACnD,MAAMQ,CAAC,GACLJ,QAAQ,GACRE,IAAI,CAACG,GAAG,CACN,EAAE,CAAC,GAAGV,MAAM,CAACW,YAAY,CAAC,IAAIZ,GAAG,GAAGG,cAAc,CAAC,GAAGP,YACxD,CAAC;EACHG,SAAS,CAACM,OAAO,GAAGA,OAAO,GAAIK,CAAC,GAAGT,MAAM,CAACY,cAAc,GAAGN,SAAS,GAAI,IAAI;EAC5ER,SAAS,CAACO,QAAQ,GAAGI,CAAC;EACtBX,SAAS,CAACG,aAAa,GAAGF,GAAG;EAE7B,IAAIC,MAAM,CAACa,KAAK,EAAE;IAChB,IAAIV,eAAe,GAAG,CAAC,IAAIL,SAAS,CAACM,OAAO,IAAIJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC,EAAE;MAC/Df,SAAS,CAACM,OAAO,GAAGJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC;MACnC,OAAO,IAAI;IACb,CAAC,MAAM,IAAIV,eAAe,GAAG,CAAC,IAAIL,SAAS,CAACM,OAAO,IAAIJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC,EAAE;MACtEf,SAAS,CAACM,OAAO,GAAGJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC;MACnC,OAAO,IAAI;IACb;EACF;EACA,OAAON,IAAI,CAACO,GAAG,CAACL,CAAC,CAAC,GAAGb,YAAY;AACnC", "ignoreList": []}