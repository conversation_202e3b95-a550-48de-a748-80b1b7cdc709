{"version": 3, "names": ["LayoutAnimationType", "getEasingByName", "resetStyle", "component", "style", "animationName", "position", "top", "left", "margin", "width", "height", "showChildren", "parent", "childrenDisplayProperty", "shouldShow", "i", "children", "length", "child", "display", "get", "set", "prepareParent", "element", "dummy", "animationConfig", "transitionData", "easing", "easingX", "Map", "originalBackgroundColor", "backgroundColor", "onFinalize", "contains", "<PERSON><PERSON><PERSON><PERSON>", "animationCancelCallback", "removeEventListener", "animationEndCallback", "addEventListener", "append<PERSON><PERSON><PERSON>", "prepareDummy", "dummyTransitionKeyframeName", "dummyAnimationConfig", "animationType", "LAYOUT", "duration", "delay", "easingY", "callback", "reversed", "cloneNode", "prepareCurvedTransition", "CurvedTransition", "keyframeXName", "keyframeYName", "keyframeXObj", "name", "transform", "translateX", "scale", "scaleX", "scaleY", "keyframeYObj", "translateY", "firstKeyframeObj", "secondKeyframeObj"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/transition/Curved.web.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,mBAAmB,QAAQ,yBAAsB;AAK1D,SAASC,eAAe,QAAQ,kBAAe;AAE/C,SAASC,UAAUA,CAACC,SAAsB,EAAE;EAC1CA,SAAS,CAACC,KAAK,CAACC,aAAa,GAAG,EAAE,CAAC,CAAC;EACpCF,SAAS,CAACC,KAAK,CAACE,QAAQ,GAAG,UAAU;EACrCH,SAAS,CAACC,KAAK,CAACG,GAAG,GAAG,KAAK;EAC3BJ,SAAS,CAACC,KAAK,CAACI,IAAI,GAAG,KAAK;EAC5BL,SAAS,CAACC,KAAK,CAACK,MAAM,GAAG,KAAK;EAC9BN,SAAS,CAACC,KAAK,CAACM,KAAK,GAAG,MAAM;EAC9BP,SAAS,CAACC,KAAK,CAACO,MAAM,GAAG,MAAM;AACjC;AAEA,SAASC,YAAYA,CACnBC,MAAmB,EACnBC,uBAAiD,EACjDC,UAAmB,EACnB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,QAAQ,CAACC,MAAM,EAAE,EAAEF,CAAC,EAAE;IAC/C,MAAMG,KAAK,GAAGN,MAAM,CAACI,QAAQ,CAACD,CAAC,CAAgB;IAE/C,IAAID,UAAU,EAAE;MACdI,KAAK,CAACf,KAAK,CAACgB,OAAO,GAAGN,uBAAuB,CAACO,GAAG,CAACF,KAAK,CAAE;IAC3D,CAAC,MAAM;MACLL,uBAAuB,CAACQ,GAAG,CAACH,KAAK,EAAEA,KAAK,CAACf,KAAK,CAACgB,OAAO,CAAC;MACvDD,KAAK,CAACf,KAAK,CAACgB,OAAO,GAAG,MAAM;IAC9B;EACF;AACF;AAEA,SAASG,aAAaA,CACpBC,OAA8B,EAC9BC,KAA4B,EAC5BC,eAAgC,EAChCC,cAA8B,EAC9B;EACA;EACAD,eAAe,CAACE,MAAM,GAAG3B,eAAe,CACtC0B,cAAc,CAACE,OACjB,CAAC;EAED,MAAMf,uBAAuB,GAAG,IAAIgB,GAAG,CAAsB,CAAC;EAC9DlB,YAAY,CAACY,OAAO,EAAEV,uBAAuB,EAAE,KAAK,CAAC;EAErD,MAAMiB,uBAAuB,GAAGP,OAAO,CAACpB,KAAK,CAAC4B,eAAe;EAC7DR,OAAO,CAACpB,KAAK,CAAC4B,eAAe,GAAG,aAAa;EAE7C,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIT,OAAO,CAACU,QAAQ,CAACT,KAAK,CAAC,EAAE;MAC3BD,OAAO,CAACW,WAAW,CAACV,KAAK,CAAC;IAC5B;IAEAb,YAAY,CAACY,OAAO,EAAEV,uBAAuB,EAAE,IAAI,CAAC;IAEpDU,OAAO,CAACpB,KAAK,CAAC4B,eAAe,GAAGD,uBAAuB;EACzD,CAAC;EAED,MAAMK,uBAAuB,GAAGA,CAAA,KAAM;IACpCH,UAAU,CAAC,CAAC;IACZT,OAAO,CAACa,mBAAmB,CAAC,iBAAiB,EAAED,uBAAuB,CAAC;EACzE,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCL,UAAU,CAAC,CAAC;IACZT,OAAO,CAACa,mBAAmB,CAAC,cAAc,EAAEC,oBAAoB,CAAC;EACnE,CAAC;EAEDd,OAAO,CAACe,gBAAgB,CAAC,cAAc,EAAED,oBAAoB,CAAC;EAC9Dd,OAAO,CAACe,gBAAgB,CAAC,iBAAiB,EAAEH,uBAAuB,CAAC;EAEpEZ,OAAO,CAACgB,WAAW,CAACf,KAAK,CAAC;AAC5B;AAEA,SAASgB,YAAYA,CACnBjB,OAA8B,EAC9BE,eAAgC,EAChCC,cAA8B,EAC9Be,2BAAmC,EACnC;EACA,MAAMC,oBAAqC,GAAG;IAC5CtC,aAAa,EAAEqC,2BAA2B;IAC1CE,aAAa,EAAE5C,mBAAmB,CAAC6C,MAAM;IACzCC,QAAQ,EAAEpB,eAAe,CAACoB,QAAQ;IAClCC,KAAK,EAAErB,eAAe,CAACqB,KAAK;IAC5BnB,MAAM,EAAE3B,eAAe,CAAC0B,cAAc,CAACqB,OAA0B,CAAC;IAClEC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMzB,KAAK,GAAGD,OAAO,CAAC2B,SAAS,CAAC,IAAI,CAA0B;EAC9DjD,UAAU,CAACuB,KAAK,CAAC;EAEjB,OAAO;IAAEA,KAAK;IAAEkB;EAAqB,CAAC;AACxC;AAEA,OAAO,SAASS,uBAAuBA,CACrC5B,OAA8B,EAC9BE,eAAgC,EAChCC,cAA8B,EAC9Be,2BAAmC,EACnC;EACA,MAAM;IAAEjB,KAAK;IAAEkB;EAAqB,CAAC,GAAGF,YAAY,CAClDjB,OAAO,EACPE,eAAe,EACfC,cAAc,EACde,2BACF,CAAC;EAEDnB,aAAa,CAACC,OAAO,EAAEC,KAAK,EAAEC,eAAe,EAAEC,cAAc,CAAC;EAE9D,OAAO;IAAEF,KAAK;IAAEkB;EAAqB,CAAC;AACxC;AAEA,OAAO,SAASU,gBAAgBA,CAC9BC,aAAqB,EACrBC,aAAqB,EACrB5B,cAA8B,EAC9B;EACA,MAAM6B,YAAY,GAAG;IACnBC,IAAI,EAAEH,aAAa;IACnBlD,KAAK,EAAE;MACL,CAAC,EAAE;QACDsD,SAAS,EAAE,CACT;UACEC,UAAU,EAAE,GAAGhC,cAAc,CAACgC,UAAU,IAAI;UAC5CC,KAAK,EAAE,GAAGjC,cAAc,CAACkC,MAAM,IAAIlC,cAAc,CAACmC,MAAM;QAC1D,CAAC;MAEL;IACF,CAAC;IACDhB,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMiB,YAAY,GAAG;IACnBN,IAAI,EAAEF,aAAa;IACnBnD,KAAK,EAAE;MACL,CAAC,EAAE;QACDsD,SAAS,EAAE,CACT;UACEM,UAAU,EAAE,GAAGrC,cAAc,CAACqC,UAAU,IAAI;UAC5CJ,KAAK,EAAE,GAAGjC,cAAc,CAACkC,MAAM,IAAIlC,cAAc,CAACmC,MAAM;QAC1D,CAAC;MAEL;IACF,CAAC;IACDhB,QAAQ,EAAE;EACZ,CAAC;EAED,OAAO;IACLmB,gBAAgB,EAAET,YAAY;IAC9BU,iBAAiB,EAAEH;EACrB,CAAC;AACH", "ignoreList": []}