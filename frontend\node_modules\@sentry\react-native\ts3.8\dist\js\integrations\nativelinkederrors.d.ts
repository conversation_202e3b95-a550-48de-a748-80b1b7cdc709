import type { EventProcessor, Hub, Integration } from '@sentry/types';
interface LinkedErrorsOptions {
    key: string;
    limit: number;
}
/**
 * Processes JS and RN native linked errors.
 */
export declare class NativeLinkedErrors implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    /**
     * @inheritDoc
     */
    name: string;
    private readonly _key;
    private readonly _limit;
    private _nativePackage;
    /**
     * @inheritDoc
     */
    constructor(options?: Partial<LinkedErrorsOptions>);
    /**
     * @inheritDoc
     */
    setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void;
    /**
     * Enriches passed event with linked exceptions and native debug meta images.
     */
    private _handler;
    /**
     * Walks linked errors and created Sentry exceptions chain.
     * Collects debug images from native errors stack frames.
     */
    private _walkErrorTree;
    /**
     * Converts a Java Throwable to an SentryException
     */
    private _exceptionFromJavaStackElements;
    /**
     * Converts StackAddresses to a SentryException with DebugMetaImages
     */
    private _exceptionFromAppleStackReturnAddresses;
    /**
     * Fetches the native package/image name from the native layer
     */
    private _fetchNativePackage;
    /**
     * Fetches native debug image information on iOS
     */
    private _fetchNativeStackFrames;
}
export {};
//# sourceMappingURL=nativelinkederrors.d.ts.map
