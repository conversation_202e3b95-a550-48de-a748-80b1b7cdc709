/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/account-details`; params?: Router.UnknownInputParams; } | { pathname: `/add-beneficiary`; params?: Router.UnknownInputParams; } | { pathname: `/add-money`; params?: Router.UnknownInputParams; } | { pathname: `/bill-payments`; params?: Router.UnknownInputParams; } | { pathname: `/change-password`; params?: Router.UnknownInputParams; } | { pathname: `/contact-support`; params?: Router.UnknownInputParams; } | { pathname: `/emi-calculator`; params?: Router.UnknownInputParams; } | { pathname: `/fixed-deposits`; params?: Router.UnknownInputParams; } | { pathname: `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/help-center`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/insurance`; params?: Router.UnknownInputParams; } | { pathname: `/investments`; params?: Router.UnknownInputParams; } | { pathname: `/loans`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/logout`; params?: Router.UnknownInputParams; } | { pathname: `/mutual-funds`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/open-account`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/support`; params?: Router.UnknownInputParams; } | { pathname: `/transfer`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/enhanced-login` | `/enhanced-login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/accounts` | `/accounts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/qr-scanner` | `/qr-scanner`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/account-details.test`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/account-details`; params?: Router.UnknownOutputParams; } | { pathname: `/add-beneficiary`; params?: Router.UnknownOutputParams; } | { pathname: `/add-money`; params?: Router.UnknownOutputParams; } | { pathname: `/bill-payments`; params?: Router.UnknownOutputParams; } | { pathname: `/change-password`; params?: Router.UnknownOutputParams; } | { pathname: `/contact-support`; params?: Router.UnknownOutputParams; } | { pathname: `/emi-calculator`; params?: Router.UnknownOutputParams; } | { pathname: `/fixed-deposits`; params?: Router.UnknownOutputParams; } | { pathname: `/forgot-password`; params?: Router.UnknownOutputParams; } | { pathname: `/help-center`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/insurance`; params?: Router.UnknownOutputParams; } | { pathname: `/investments`; params?: Router.UnknownOutputParams; } | { pathname: `/loans`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/logout`; params?: Router.UnknownOutputParams; } | { pathname: `/mutual-funds`; params?: Router.UnknownOutputParams; } | { pathname: `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `/open-account`; params?: Router.UnknownOutputParams; } | { pathname: `/search`; params?: Router.UnknownOutputParams; } | { pathname: `/support`; params?: Router.UnknownOutputParams; } | { pathname: `/transfer`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/enhanced-login` | `/enhanced-login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/accounts` | `/accounts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/qr-scanner` | `/qr-scanner`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/account-details.test`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/account-details${`?${string}` | `#${string}` | ''}` | `/add-beneficiary${`?${string}` | `#${string}` | ''}` | `/add-money${`?${string}` | `#${string}` | ''}` | `/bill-payments${`?${string}` | `#${string}` | ''}` | `/change-password${`?${string}` | `#${string}` | ''}` | `/contact-support${`?${string}` | `#${string}` | ''}` | `/emi-calculator${`?${string}` | `#${string}` | ''}` | `/fixed-deposits${`?${string}` | `#${string}` | ''}` | `/forgot-password${`?${string}` | `#${string}` | ''}` | `/help-center${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/insurance${`?${string}` | `#${string}` | ''}` | `/investments${`?${string}` | `#${string}` | ''}` | `/loans${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/logout${`?${string}` | `#${string}` | ''}` | `/mutual-funds${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `/open-account${`?${string}` | `#${string}` | ''}` | `/search${`?${string}` | `#${string}` | ''}` | `/support${`?${string}` | `#${string}` | ''}` | `/transfer${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/enhanced-login${`?${string}` | `#${string}` | ''}` | `/enhanced-login${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/accounts${`?${string}` | `#${string}` | ''}` | `/accounts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/qr-scanner${`?${string}` | `#${string}` | ''}` | `/qr-scanner${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/transactions${`?${string}` | `#${string}` | ''}` | `/transactions${`?${string}` | `#${string}` | ''}` | `/__tests__/account-details.test${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/account-details`; params?: Router.UnknownInputParams; } | { pathname: `/add-beneficiary`; params?: Router.UnknownInputParams; } | { pathname: `/add-money`; params?: Router.UnknownInputParams; } | { pathname: `/bill-payments`; params?: Router.UnknownInputParams; } | { pathname: `/change-password`; params?: Router.UnknownInputParams; } | { pathname: `/contact-support`; params?: Router.UnknownInputParams; } | { pathname: `/emi-calculator`; params?: Router.UnknownInputParams; } | { pathname: `/fixed-deposits`; params?: Router.UnknownInputParams; } | { pathname: `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/help-center`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/insurance`; params?: Router.UnknownInputParams; } | { pathname: `/investments`; params?: Router.UnknownInputParams; } | { pathname: `/loans`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/logout`; params?: Router.UnknownInputParams; } | { pathname: `/mutual-funds`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/open-account`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/support`; params?: Router.UnknownInputParams; } | { pathname: `/transfer`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/enhanced-login` | `/enhanced-login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/accounts` | `/accounts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/qr-scanner` | `/qr-scanner`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/account-details.test`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
