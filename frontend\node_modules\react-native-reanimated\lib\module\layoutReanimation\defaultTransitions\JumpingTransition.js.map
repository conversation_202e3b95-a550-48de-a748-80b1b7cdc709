{"version": 3, "names": ["withSequence", "withTiming", "Easing", "BaseAnimationBuilder", "JumpingTransition", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "duration", "durationV", "halfDuration", "config", "values", "d", "Math", "max", "abs", "targetOriginX", "currentOriginX", "targetOriginY", "currentOriginY", "initialValues", "originX", "originY", "width", "currentWidth", "height", "currentHeight", "animations", "min", "easing", "out", "exp", "bounce", "targetWidth", "targetHeight"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultTransitions/JumpingTransition.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,YAAY,EAAEC,UAAU,QAAQ,0BAAiB;AAK1D,SAASC,MAAM,QAAQ,iBAAc;AACrC,SAASC,oBAAoB,QAAQ,8BAAqB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,SACpBD,oBAAoB,CAE9B;EACE,OAAOE,UAAU,GAAG,mBAAmB;EAEvC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,iBAAiB,CAAC,CAAC;EAChC;EAEAG,KAAK,GAAGA,CAAA,KAA+B;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS,IAAI,GAAG;IACtC,MAAMC,YAAY,GAAGF,QAAQ,GAAG,CAAC;IACjC,MAAMG,MAAM,GAAG;MAAEH;IAAS,CAAC;IAE3B,OAAQI,MAAM,IAAK;MACjB,SAAS;;MACT,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAChBD,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACK,aAAa,GAAGL,MAAM,CAACM,cAAc,CAAC,EACtDJ,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACO,aAAa,GAAGP,MAAM,CAACQ,cAAc,CACvD,CAAC;MACD,OAAO;QACLC,aAAa,EAAE;UACbC,OAAO,EAAEV,MAAM,CAACM,cAAc;UAC9BK,OAAO,EAAEX,MAAM,CAACQ,cAAc;UAC9BI,KAAK,EAAEZ,MAAM,CAACa,YAAY;UAC1BC,MAAM,EAAEd,MAAM,CAACe;QACjB,CAAC;QACDC,UAAU,EAAE;UACVN,OAAO,EAAEpB,aAAa,CACpBI,KAAK,EACLX,UAAU,CAACiB,MAAM,CAACK,aAAa,EAAEN,MAAM,CACzC,CAAC;UACDY,OAAO,EAAErB,aAAa,CACpBI,KAAK,EACLZ,YAAY,CACVC,UAAU,CACRmB,IAAI,CAACe,GAAG,CAACjB,MAAM,CAACO,aAAa,EAAEP,MAAM,CAACQ,cAAc,CAAC,GAAGP,CAAC,EACzD;YACEL,QAAQ,EAAEE,YAAY;YACtBoB,MAAM,EAAElC,MAAM,CAACmC,GAAG,CAACnC,MAAM,CAACoC,GAAG;UAC/B,CACF,CAAC,EACDrC,UAAU,CAACiB,MAAM,CAACO,aAAa,EAAE;YAC/B,GAAGR,MAAM;YACTH,QAAQ,EAAEE,YAAY;YACtBoB,MAAM,EAAElC,MAAM,CAACqC;UACjB,CAAC,CACH,CACF,CAAC;UACDT,KAAK,EAAEtB,aAAa,CAACI,KAAK,EAAEX,UAAU,CAACiB,MAAM,CAACsB,WAAW,EAAEvB,MAAM,CAAC,CAAC;UACnEe,MAAM,EAAExB,aAAa,CAACI,KAAK,EAAEX,UAAU,CAACiB,MAAM,CAACuB,YAAY,EAAExB,MAAM,CAAC;QACtE,CAAC;QACDP;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}