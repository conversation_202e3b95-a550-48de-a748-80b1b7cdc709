import type { Envelope, Event, Profile, ThreadCpuProfile } from '@sentry/types';
import type { CombinedProfileEvent, HermesProfileEvent, RawThreadCpuProfile } from './types';
/**
 *
 */
export declare function isValidProfile(profile: ThreadCpuProfile): profile is RawThreadCpuProfile & {
    profile_id: string;
};
/**
 * Finds transactions with profile_id context in the envelope
 * @param envelope
 * @returns
 */
export declare function findProfiledTransactionsFromEnvelope(envelope: Envelope): Event[];
/**
 * Creates a profiling envelope item, if the profile does not pass validation, returns null.
 * @param event
 * @returns {Profile | null}
 */
export declare function enrichCombinedProfileWithEventContext(profile_id: string, profile: CombinedProfileEvent, event: Event): Profile | null;
/**
 * Creates profiling event compatible carrier Object from raw Hermes profile.
 */
export declare function createHermesProfilingEvent(profile: RawThreadCpuProfile): HermesProfileEvent;
/**
 * Adds items to envelope if they are not already present - mutates the envelope.
 * @param envelope
 */
export declare function addProfilesToEnvelope(envelope: Envelope, profiles: Profile[]): Envelope;
//# sourceMappingURL=utils.d.ts.map
