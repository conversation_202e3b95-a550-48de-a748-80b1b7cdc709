import type { Hub } from '@sentry/core';
import type { EventProcessor, Integration, ThreadCpuProfile } from '@sentry/types';
import type { NativeProfileEvent } from './nativeTypes';
import type { CombinedProfileEvent, HermesProfileEvent } from './types';
/**
 * Profiling integration creates a profile for each transaction and adds it to the event envelope.
 *
 * @experimental
 */
export declare class HermesProfiling implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    /**
     * @inheritDoc
     */
    name: string;
    private _getCurrentHub?;
    private _currentProfile;
    private _currentProfileTimeout;
    /**
     * @inheritDoc
     */
    setupOnce(_: (e: EventProcessor) => void, getCurrentHub: () => Hub): void;
    private _startCurrentProfileForActiveTransaction;
    private _startCurrentProfile;
    private _shouldStartProfiling;
    /**
     * Starts a new profile and links it to the transaction.
     */
    private _startNewProfile;
    /**
     * Stops profiling and adds the profile to the queue to be processed on beforeEnvelope.
     */
    private _finishCurrentProfile;
    private _createProfileEventFor;
    private _clearCurrentProfileTimeout;
}
/**
 * Starts Profilers and returns the timestamp when profiling started in nanoseconds.
 */
export declare function startProfiling(): number | null;
/**
 * Stops Profilers and returns collected combined profile.
 */
export declare function stopProfiling(): CombinedProfileEvent | null;
/**
 * Merges Hermes and Native profile events into one.
 */
export declare function addNativeProfileToHermesProfile(hermes: HermesProfileEvent, native: NativeProfileEvent): CombinedProfileEvent;
/**
 * Merges Hermes And Native profiles into one.
 */
export declare function addNativeThreadCpuProfileToHermes(hermes: ThreadCpuProfile, native: ThreadCpuProfile, hermes_active_thread_id: string | undefined): CombinedProfileEvent['profile'];
//# sourceMappingURL=integration.d.ts.map
