import type { EventProcessor, Hub, Integration } from '@sentry/types';
/** Load device context from native. */
export declare class DeviceContext implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    /**
     * @inheritDoc
     */
    name: string;
    /**
     * @inheritDoc
     */
    setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void;
}
//# sourceMappingURL=devicecontext.d.ts.map
