const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Get the default Expo configuration
const defaultConfig = getDefaultConfig(__dirname);

// Customize the configuration
const config = {
  ...defaultConfig,
  resolver: {
    ...defaultConfig.resolver,
    // Add any file extensions that might be causing issues
    sourceExts: [...defaultConfig.resolver.sourceExts, 'mjs', 'cjs'],
    // Add aliases for common paths
    extraNodeModules: {
      '@': path.resolve(__dirname),
      '~': path.resolve(__dirname),
    },
    // Prevent resolution of problematic paths
    resolverMainFields: ['react-native', 'browser', 'main'],
    platforms: ['ios', 'android', 'native', 'web'],
  },
  transformer: {
    ...defaultConfig.transformer,
    // Disable inline requires to prevent anonymous module issues
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false, // Prevent inline requires that can cause anonymous file issues
      },
    }),
    // Use Expo's default babel transformer (no need to specify babelTransformerPath)
    // The default Expo configuration already includes the correct transformer
  },
  serializer: {
    ...defaultConfig.serializer,
    // Customize module ID generation to avoid anonymous modules
    createModuleIdFactory: () => {
      return (path) => {
        // Ensure we never return undefined or null module IDs
        if (!path || typeof path !== 'string') {
          console.warn('Metro: Invalid module path detected:', path);
          return `__invalid_module_${Date.now()}`;
        }

        // Check for problematic paths and handle them
        if (path.includes('<anonymous>') || path.includes('undefined') || path.includes('null')) {
          console.warn('Metro: Problematic module path detected:', path);
          return `__problematic_module_${Date.now()}`;
        }

        // Use default behavior for valid paths
        return path;
      };
    },
  },
  server: {
    ...defaultConfig.server,
    // Add better error handling for file resolution
    enhanceMiddleware: (middleware) => {
      return (req, res, next) => {
        try {
          // Handle requests for files that might not exist or have problematic paths
          if (req.url && (
            req.url.includes('<anonymous>') ||
            req.url.includes('undefined') ||
            req.url.includes('null') ||
            req.url.includes('NaN') ||
            req.url === '' ||
            req.url === '/'
          )) {
            console.warn('Metro: Skipping problematic file request:', req.url);
            res.status(404).json({ error: 'File not found' });
            return;
          }

          return middleware(req, res, next);
        } catch (error) {
          console.error('Metro middleware error:', error);
          res.status(500).json({ error: 'Internal server error' });
        }
      };
    },
  },
  symbolicator: {
    // Disable symbolication for problematic files
    customizeFrame: (frame) => {
      // Skip frames with problematic file paths
      if (frame.file && (
        frame.file.includes('<anonymous>') ||
        frame.file.includes('undefined') ||
        frame.file.includes('null') ||
        !frame.file ||
        frame.file === ''
      )) {
        return null; // Skip this frame
      }
      return frame;
    },
  },
  watchFolders: [
    // Explicitly define watch folders to avoid scanning problematic directories
    path.resolve(__dirname, 'app'),
    path.resolve(__dirname, 'components'),
    path.resolve(__dirname, 'hooks'),
    path.resolve(__dirname, 'services'),
    path.resolve(__dirname, 'utils'),
    path.resolve(__dirname, 'types'),
    path.resolve(__dirname, 'constants'),
    path.resolve(__dirname, 'contexts'),
    path.resolve(__dirname, 'styles'),
  ],
};

module.exports = config;