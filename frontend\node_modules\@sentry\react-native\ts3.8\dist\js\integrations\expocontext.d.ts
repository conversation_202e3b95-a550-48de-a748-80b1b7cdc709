import type { EventProcessor, Hub, Integration } from '@sentry/types';
/** Load device context from expo modules. */
export declare class ExpoContext implements Integration {
    /**
     * @inheritDoc
     */
    static id: string;
    /**
     * @inheritDoc
     */
    name: string;
    /**
     * @inheritDoc
     */
    setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void;
}
//# sourceMappingURL=expocontext.d.ts.map
