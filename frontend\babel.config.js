module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'babel-plugin-module-resolver',
        {
          alias: {
            '@': './',
          },
          extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
        },
      ],
      // Note: Removed @babel/plugin-transform-modules-commonjs as it conflicts with Expo's default setup
      // Expo's babel-preset-expo already handles module transformation properly
    ],
    // Note: Removed production-specific plugins to avoid conflicts with Expo's default setup
    // Expo's babel-preset-expo already handles production optimizations
    // Add source map configuration to help with debugging
    sourceMaps: true,
    retainLines: true,
  };
};
