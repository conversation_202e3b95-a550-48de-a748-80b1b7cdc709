{"version": 3, "names": ["dispatchCommand", "getRelativeCoords", "measure", "scrollTo", "setGestureState", "setNativeProps"], "sourceRoot": "../../../src", "sources": ["platformFunctions/index.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,mBAAmB;AAEnD,SAASC,iBAAiB,QAAQ,wBAAqB;AACvD,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB", "ignoreList": []}